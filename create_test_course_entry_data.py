"""
Создание тестовых данных для входных тестов курса
"""
import asyncio
from database import (
    CourseEntryTestResultRepository, StudentRepository, SubjectRepository, 
    QuestionRepository, HomeworkRepository, LessonRepository
)


async def create_test_course_entry_results():
    """Создание тестовых результатов входных тестов курса"""
    try:
        print("📊 Создание тестовых результатов входных тестов курса...")
        
        # Проверяем, есть ли уже результаты
        existing_results = await CourseEntryTestResultRepository.get_all()
        if existing_results:
            print("   ⚠️ Результаты входных тестов курса уже существуют, пропускаем создание")
            return
        
        # Получаем студентов и предметы
        students = await StudentRepository.get_all()
        subjects = await SubjectRepository.get_all()
        
        if not students or not subjects:
            print("   ⚠️ Студенты или предметы не найдены, пропускаем создание результатов")
            return
        
        # Создаем тестовые результаты для некоторых студентов
        test_cases = [
            {
                "student_name": "Аружан Ахметова",
                "subject_name": "Математика",
                "correct_count": 25,  # из 30
            },
            {
                "student_name": "Ерасыл Мухамедов", 
                "subject_name": "Математика",
                "correct_count": 18,  # из 30
            },
            {
                "student_name": "Муханбетжан Олжас",
                "subject_name": "Python",
                "correct_count": 28,  # из 30
            }
        ]
        
        created_count = 0
        
        for test_case in test_cases:
            # Находим студента
            student = None
            for s in students:
                if s.user.name == test_case["student_name"]:
                    student = s
                    break
            
            if not student:
                print(f"   ⚠️ Студент '{test_case['student_name']}' не найден")
                continue
            
            # Находим предмет
            subject = None
            for subj in subjects:
                if subj.name == test_case["subject_name"]:
                    subject = subj
                    break
            
            if not subject:
                print(f"   ⚠️ Предмет '{test_case['subject_name']}' не найден")
                continue
            
            # Получаем случайные вопросы для предмета
            questions = await CourseEntryTestResultRepository.get_random_questions_for_subject(subject.id, 30)
            
            if len(questions) < 30:
                print(f"   ⚠️ Недостаточно вопросов для предмета '{subject.name}' (найдено: {len(questions)})")
                continue
            
            # Создаем результаты ответов
            question_results = []
            correct_count = test_case["correct_count"]
            
            for i, question in enumerate(questions):
                # Первые correct_count вопросов - правильные, остальные - неправильные
                is_correct = i < correct_count
                
                # Выбираем ответ
                correct_answer = None
                wrong_answer = None
                
                for answer in question.answer_options:
                    if answer.is_correct:
                        correct_answer = answer
                    else:
                        wrong_answer = answer
                
                selected_answer = correct_answer if is_correct else wrong_answer
                
                question_results.append({
                    'question_id': question.id,
                    'selected_answer_id': selected_answer.id if selected_answer else None,
                    'is_correct': is_correct,
                    'time_spent': 30 + i,  # Разное время ответа
                    'microtopic_number': question.microtopic_number
                })
            
            # Создаем результат теста
            try:
                test_result = await CourseEntryTestResultRepository.create_test_result(
                    student_id=student.id,
                    subject_id=subject.id,
                    question_results=question_results
                )
                
                print(f"   ✅ Результат входного теста создан для {student.user.name} по предмету {subject.name} ({test_result.score_percentage}%)")
                created_count += 1
                
            except Exception as e:
                print(f"   ❌ Ошибка при создании результата для {student.user.name}: {e}")
        
        print(f"📊 Создание тестовых результатов входных тестов курса завершено! Создано: {created_count}")
        
    except Exception as e:
        print(f"❌ Ошибка при создании тестовых результатов входных тестов курса: {e}")


if __name__ == "__main__":
    asyncio.run(create_test_course_entry_results())
