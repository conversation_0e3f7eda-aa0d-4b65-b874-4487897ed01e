import asyncio
from database import get_db_session
from sqlalchemy import text

async def drop_course_entry_tables():
    async with get_db_session() as session:
        try:
            # Удаляем таблицы в правильном порядке (сначала зависимые)
            await session.execute(text("DROP TABLE IF EXISTS course_entry_question_results CASCADE"))
            await session.execute(text("DROP TABLE IF EXISTS course_entry_test_results CASCADE"))
            await session.commit()
            print("✅ Старые таблицы входного теста курса удалены")
        except Exception as e:
            print(f"❌ Ошибка при удалении таблиц: {e}")
            await session.rollback()

if __name__ == "__main__":
    asyncio.run(drop_course_entry_tables())
