import asyncio
from database import StudentRepository

async def check_olzhas_groups():
    students = await StudentRepository.get_all()
    
    for student in students:
        if "Олжас" in student.user.name:
            print(f"Студент: {student.user.name}")
            print(f"Группы: {[group.name for group in student.groups]}")
            break

if __name__ == "__main__":
    asyncio.run(check_olzhas_groups())
