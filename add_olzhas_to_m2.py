import asyncio
from database import StudentRepository, GroupRepository, get_db_session
from sqlalchemy import text

async def add_olzhas_to_m2():
    try:
        # Находим Олжаса
        students = await StudentRepository.get_all()
        olzhas = None
        for student in students:
            if "Олжас" in student.user.name:
                olzhas = student
                break
        
        if not olzhas:
            print("❌ Олжас не найден")
            return
        
        # Находим группу M-2
        groups = await GroupRepository.get_all()
        m2_group = None
        for group in groups:
            if group.name == "М-2":
                m2_group = group
                break
        
        if not m2_group:
            print("❌ Группа М-2 не найдена")
            return
        
        # Проверяем, не состоит ли уже в группе
        async with get_db_session() as session:
            result = await session.execute(
                text("SELECT 1 FROM student_groups WHERE student_id = :student_id AND group_id = :group_id"),
                {"student_id": olzhas.id, "group_id": m2_group.id}
            )
            
            if result.fetchone():
                print("✅ Олжас уже состоит в группе М-2")
                return
            
            # Добавляем в группу
            await session.execute(
                text("INSERT INTO student_groups (student_id, group_id) VALUES (:student_id, :group_id)"),
                {"student_id": olzhas.id, "group_id": m2_group.id}
            )
            await session.commit()
            print(f"✅ Олжас добавлен в группу М-2")
        
        # Проверяем результат
        updated_student = await StudentRepository.get_by_id(olzhas.id)
        print(f"Группы Олжаса: {[group.name for group in updated_student.groups]}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(add_olzhas_to_m2())
