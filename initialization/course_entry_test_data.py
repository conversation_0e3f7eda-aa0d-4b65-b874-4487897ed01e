"""
Создание тестовых данных для входных тестов курса
"""
from database import (
    CourseEntryTestResultRepository, StudentRepository, CourseRepository, 
    SubjectRepository, GroupRepository
)


async def create_course_entry_test_results():
    """Создание тестовых результатов входных тестов курса"""
    try:
        print("📊 Создание тестовых результатов входных тестов курса...")
        
        # Проверяем, есть ли уже результаты
        existing_results = await CourseEntryTestResultRepository.get_all()
        if existing_results:
            print("   ⚠️ Результаты входных тестов курса уже существуют, пропускаем создание")
            return
        
        # Получаем всех студентов
        students = await StudentRepository.get_all()
        if not students:
            print("   ⚠️ Студенты не найдены, пропускаем создание результатов")
            return
        
        # Получаем курсы и предметы
        courses = await CourseRepository.get_all()
        subjects = await SubjectRepository.get_all()
        
        if not courses or not subjects:
            print("   ⚠️ Курсы или предметы не найдены, пропускаем создание результатов")
            return
        
        # Создаем тестовые результаты для некоторых студентов
        test_results_data = [
            {
                "student_name": "Аружан Ахметова",  # Студент из группы М-1 (Математика)
                "subject_name": "Математика",
                "total_questions": 20,
                "correct_answers": 16,
                "question_results": [
                    {
                        "question_text": "Решите уравнение: 2x + 5 = 13",
                        "selected_answer_text": "x = 4",
                        "correct_answer_text": "x = 4",
                        "is_correct": True,
                        "time_spent": 45,
                        "microtopic_number": 1
                    },
                    {
                        "question_text": "Найдите производную функции f(x) = x²",
                        "selected_answer_text": "2x",
                        "correct_answer_text": "2x",
                        "is_correct": True,
                        "time_spent": 30,
                        "microtopic_number": 2
                    },
                    {
                        "question_text": "Вычислите: sin(π/2)",
                        "selected_answer_text": "0",
                        "correct_answer_text": "1",
                        "is_correct": False,
                        "time_spent": 25,
                        "microtopic_number": 3
                    }
                    # Добавим еще результаты для остальных 17 вопросов
                ] + [
                    {
                        "question_text": f"Тестовый вопрос {i}",
                        "selected_answer_text": f"Ответ {i}",
                        "correct_answer_text": f"Ответ {i}",
                        "is_correct": True,
                        "time_spent": 20 + i,
                        "microtopic_number": (i % 5) + 1
                    } for i in range(4, 21)
                ]
            },
            {
                "student_name": "Ерасыл Мухамедов",  # Студент из группы М-2 (Математика)
                "subject_name": "Математика",
                "total_questions": 20,
                "correct_answers": 12,
                "question_results": [
                    {
                        "question_text": "Решите уравнение: 2x + 5 = 13",
                        "selected_answer_text": "x = 3",
                        "correct_answer_text": "x = 4",
                        "is_correct": False,
                        "time_spent": 60,
                        "microtopic_number": 1
                    },
                    {
                        "question_text": "Найдите производную функции f(x) = x²",
                        "selected_answer_text": "2x",
                        "correct_answer_text": "2x",
                        "is_correct": True,
                        "time_spent": 35,
                        "microtopic_number": 2
                    }
                ] + [
                    {
                        "question_text": f"Тестовый вопрос {i}",
                        "selected_answer_text": f"Ответ {i}",
                        "correct_answer_text": f"Ответ {i}",
                        "is_correct": i <= 12,  # Первые 12 правильные, остальные неправильные
                        "time_spent": 25 + i,
                        "microtopic_number": (i % 5) + 1
                    } for i in range(3, 21)
                ]
            },
            {
                "student_name": "Муханбетжан Олжас",  # Студент из группы PY-1 (Python)
                "subject_name": "Python",
                "total_questions": 15,
                "correct_answers": 14,
                "question_results": [
                    {
                        "question_text": "Что выведет print('Hello, World!')?",
                        "selected_answer_text": "Hello, World!",
                        "correct_answer_text": "Hello, World!",
                        "is_correct": True,
                        "time_spent": 15,
                        "microtopic_number": 1
                    },
                    {
                        "question_text": "Какой тип данных у переменной x = [1, 2, 3]?",
                        "selected_answer_text": "list",
                        "correct_answer_text": "list",
                        "is_correct": True,
                        "time_spent": 20,
                        "microtopic_number": 2
                    }
                ] + [
                    {
                        "question_text": f"Python вопрос {i}",
                        "selected_answer_text": f"Ответ {i}",
                        "correct_answer_text": f"Ответ {i}",
                        "is_correct": i <= 14,  # 14 из 15 правильных
                        "time_spent": 18 + i,
                        "microtopic_number": (i % 3) + 1
                    } for i in range(3, 16)
                ]
            }
        ]
        
        created_count = 0
        
        for result_data in test_results_data:
            # Находим студента по имени
            student = None
            for s in students:
                if s.user.name == result_data["student_name"]:
                    student = s
                    break
            
            if not student:
                print(f"   ⚠️ Студент '{result_data['student_name']}' не найден")
                continue
            
            # Находим предмет
            subject = None
            for subj in subjects:
                if subj.name == result_data["subject_name"]:
                    subject = subj
                    break
            
            if not subject:
                print(f"   ⚠️ Предмет '{result_data['subject_name']}' не найден")
                continue
            
            # Находим курс для предмета
            course = None
            for c in courses:
                if any(s.id == subject.id for s in c.subjects):
                    course = c
                    break
            
            if not course:
                print(f"   ⚠️ Курс для предмета '{result_data['subject_name']}' не найден")
                continue
            
            # Создаем результат теста
            try:
                test_result = await CourseEntryTestResultRepository.create(
                    student_id=student.id,
                    course_id=course.id,
                    subject_id=subject.id,
                    total_questions=result_data["total_questions"],
                    correct_answers=result_data["correct_answers"],
                    question_results=result_data["question_results"]
                )
                
                print(f"   ✅ Результат входного теста создан для {student.user.name} по предмету {subject.name} ({test_result.score_percentage}%)")
                created_count += 1
                
            except Exception as e:
                print(f"   ❌ Ошибка при создании результата для {student.user.name}: {e}")
        
        print(f"📊 Создание тестовых результатов входных тестов курса завершено! Создано: {created_count}")
        
    except Exception as e:
        print(f"❌ Ошибка при создании тестовых результатов входных тестов курса: {e}")
