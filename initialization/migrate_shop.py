"""
Миграция для добавления функциональности магазина
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database import get_db_session
from sqlalchemy import text


async def add_coins_column():
    """Добавить колонку coins в таблицу students"""
    print("Добавление колонки 'coins' в таблицу 'students'...")
    
    async with get_db_session() as session:
        try:
            # Проверяем, существует ли уже колонка
            check_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'students' AND column_name = 'coins'
            """)
            result = await session.execute(check_query)
            existing_column = result.fetchone()
            
            if existing_column:
                print("   ⚠️ Колонка 'coins' уже существует в таблице 'students'")
                return True
            
            # Добавляем колонку
            alter_query = text("""
                ALTER TABLE students 
                ADD COLUMN coins INTEGER DEFAULT 0
            """)
            await session.execute(alter_query)
            await session.commit()
            
            print("   ✅ Колонка 'coins' успешно добавлена в таблицу 'students'")
            return True
            
        except Exception as e:
            print(f"   ❌ Ошибка при добавлении колонки 'coins': {e}")
            await session.rollback()
            return False


async def create_shop_tables():
    """Создать таблицы для магазина"""
    print("Создание таблиц для магазина...")
    
    async with get_db_session() as session:
        try:
            # Проверяем, существует ли таблица shop_items
            check_shop_items = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'shop_items'
            """)
            result = await session.execute(check_shop_items)
            shop_items_exists = result.fetchone()
            
            if not shop_items_exists:
                # Создаем таблицу shop_items
                create_shop_items = text("""
                    CREATE TABLE shop_items (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        price INTEGER NOT NULL,
                        item_type VARCHAR(50) NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                await session.execute(create_shop_items)
                print("   ✅ Таблица 'shop_items' создана")
            else:
                print("   ⚠️ Таблица 'shop_items' уже существует")
            
            # Проверяем, существует ли таблица student_purchases
            check_purchases = text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_name = 'student_purchases'
            """)
            result = await session.execute(check_purchases)
            purchases_exists = result.fetchone()

            if not purchases_exists:
                # Создаем таблицу student_purchases
                create_purchases = text("""
                    CREATE TABLE student_purchases (
                        id SERIAL PRIMARY KEY,
                        student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
                        item_id INTEGER NOT NULL REFERENCES shop_items(id) ON DELETE CASCADE,
                        price_paid INTEGER NOT NULL,
                        is_used BOOLEAN DEFAULT FALSE,
                        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                await session.execute(create_purchases)
                print("   ✅ Таблица 'student_purchases' создана")
            else:
                print("   ⚠️ Таблица 'student_purchases' уже существует")

            # Проверяем, существует ли таблица student_bonus_tests
            check_bonus_tests = text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_name = 'student_bonus_tests'
            """)
            result = await session.execute(check_bonus_tests)
            bonus_tests_exists = result.fetchone()

            if not bonus_tests_exists:
                # Создаем таблицу student_bonus_tests
                create_bonus_tests = text("""
                    CREATE TABLE student_bonus_tests (
                        id SERIAL PRIMARY KEY,
                        student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
                        bonus_test_id INTEGER NOT NULL REFERENCES bonus_tests(id) ON DELETE CASCADE,
                        price_paid INTEGER NOT NULL,
                        is_used BOOLEAN DEFAULT FALSE,
                        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                await session.execute(create_bonus_tests)
                print("   ✅ Таблица 'student_bonus_tests' создана")
            else:
                print("   ⚠️ Таблица 'student_bonus_tests' уже существует")
            
            await session.commit()
            return True
            
        except Exception as e:
            print(f"   ❌ Ошибка при создании таблиц магазина: {e}")
            await session.rollback()
            return False


async def add_shop_items():
    """Добавить товары в магазин"""
    print("Добавление товаров в магазин...")
    
    from database import ShopItemRepository
    
    # Список товаров для магазина (без бонусных тестов - они загружаются динамически)
    shop_items = [
        {
            "name": "PDF по ошибкам",
            "description": "Подробный разбор часто встречающихся ошибок",
            "price": 80,
            "item_type": "pdf"
        },
        {
            "name": "5000 тенге",
            "description": "Денежный приз",
            "price": 150,
            "item_type": "money"
        },
        {
            "name": "Консультация с преподавателем",
            "description": "Индивидуальная консультация на 30 минут",
            "price": 120,
            "item_type": "other"
        },
        {
            "name": "Дополнительные материалы",
            "description": "Эксклюзивные материалы по предмету",
            "price": 90,
            "item_type": "pdf"
        }
    ]
    
    try:
        # Проверяем, есть ли уже товары в базе
        existing_items = await ShopItemRepository.get_all_active()
        if existing_items:
            print(f"   ⚠️ Товары уже существуют в базе данных ({len(existing_items)} шт.). Пропускаем инициализацию.")
            return True
        
        # Создаем товары
        created_count = 0
        for item_data in shop_items:
            try:
                await ShopItemRepository.create(
                    name=item_data["name"],
                    description=item_data["description"],
                    price=item_data["price"],
                    item_type=item_data["item_type"]
                )
                created_count += 1
                print(f"   ✅ Создан товар: {item_data['name']}")
            except Exception as e:
                print(f"   ❌ Ошибка при создании товара {item_data['name']}: {e}")
        
        print(f"   🎉 Инициализация товаров завершена. Создано: {created_count} товаров.")
        return True
        
    except Exception as e:
        print(f"   ❌ Ошибка при добавлении товаров: {e}")
        return False


async def main():
    """Основная функция миграции"""
    print("🔄 Запуск миграции для магазина...")
    
    # Добавляем колонку coins
    coins_success = await add_coins_column()
    
    # Создаем таблицы магазина
    tables_success = await create_shop_tables()
    
    # Добавляем товары
    items_success = False
    if coins_success and tables_success:
        items_success = await add_shop_items()
    
    if coins_success and tables_success and items_success:
        print("✅ Миграция завершена успешно!")
    else:
        print("❌ Миграция завершена с ошибками!")


if __name__ == "__main__":
    asyncio.run(main())
