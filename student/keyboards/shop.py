from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from common.keyboards import get_main_menu_back_button
from typing import List

def get_shop_menu_kb() -> InlineKeyboardMarkup:
    """Клавиатура главного меню магазина"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="💳 Обменять баллы", callback_data="exchange_points")],
        [InlineKeyboardButton(text="🛒 Каталог бонусов", callback_data="bonus_catalog")],
        [InlineKeyboardButton(text="📦 Мои бонусы", callback_data="my_bonuses")],
        *get_main_menu_back_button()
    ])

async def get_exchange_points_kb(available_points: int) -> InlineKeyboardMarkup:
    """Клавиатура для обмена баллов на монеты"""
    buttons = []

    # Варианты обмена в зависимости от доступных баллов
    exchange_options = [50, 70, 100, 150, 200]

    for amount in exchange_options:
        if available_points >= amount:
            buttons.append([InlineKeyboardButton(
                text=f"{amount} баллов → {amount} монет",
                callback_data=f"exchange_{amount}"
            )])

    if not buttons:
        buttons.append([InlineKeyboardButton(
            text="❌ Недостаточно баллов",
            callback_data="no_points"
        )])

    buttons.extend(get_main_menu_back_button())
    return InlineKeyboardMarkup(inline_keyboard=buttons)

async def get_bonus_catalog_kb(items: List, user_coins: int) -> InlineKeyboardMarkup:
    """Клавиатура каталога бонусов"""
    buttons = []

    for item in items:
        # Определяем эмодзи по типу товара
        emoji = {
            'bonus_test': '🧪',
            'pdf': '📘',
            'money': '💰',
            'other': '🎁'
        }.get(item['item_type'], '🎁')

        # Определяем callback_data в зависимости от типа товара
        if item['type'] == 'bonus_test':
            callback_prefix = "buy_bonus_"
        else:
            callback_prefix = "buy_item_"

        # Проверяем, хватает ли монет
        if user_coins >= item['price']:
            button_text = f"{emoji} {item['name']} — {item['price']} монет"
            callback_data = f"{callback_prefix}{item['id']}"
        else:
            button_text = f"❌ {emoji} {item['name']} — {item['price']} монет"
            callback_data = f"no_coins_{item['id']}"

        buttons.append([InlineKeyboardButton(
            text=button_text,
            callback_data=callback_data
        )])

    buttons.extend(get_main_menu_back_button())
    return InlineKeyboardMarkup(inline_keyboard=buttons)

def get_back_to_shop_kb() -> InlineKeyboardMarkup:
    """Клавиатура для возврата в меню магазина"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button(),
    ])