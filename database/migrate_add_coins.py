"""
Миграция для добавления колонки coins в таблицу students
"""
import asyncio
import sys
import os

# Добавляем корневую папку проекта в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database import get_db_session
from sqlalchemy import text


async def add_coins_column():
    """Добавить колонку coins в таблицу students"""
    print("Добавление колонки 'coins' в таблицу 'students'...")
    
    async with get_db_session() as session:
        try:
            # Проверяем, существует ли уже колонка
            check_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'students' AND column_name = 'coins'
            """)
            result = await session.execute(check_query)
            existing_column = result.fetchone()
            
            if existing_column:
                print("   ⚠️ Колонка 'coins' уже существует в таблице 'students'")
                return True
            
            # Добавляем колонку
            alter_query = text("""
                ALTER TABLE students 
                ADD COLUMN coins INTEGER DEFAULT 0
            """)
            await session.execute(alter_query)
            await session.commit()
            
            print("   ✅ Колонка 'coins' успешно добавлена в таблицу 'students'")
            return True
            
        except Exception as e:
            print(f"   ❌ Ошибка при добавлении колонки 'coins': {e}")
            await session.rollback()
            return False


async def create_shop_tables():
    """Создать таблицы для магазина"""
    print("Создание таблиц для магазина...")
    
    async with get_db_session() as session:
        try:
            # Проверяем, существует ли таблица shop_items
            check_shop_items = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'shop_items'
            """)
            result = await session.execute(check_shop_items)
            shop_items_exists = result.fetchone()
            
            if not shop_items_exists:
                # Создаем таблицу shop_items
                create_shop_items = text("""
                    CREATE TABLE shop_items (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(255) NOT NULL,
                        description TEXT,
                        price INTEGER NOT NULL,
                        item_type VARCHAR(50) NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                await session.execute(create_shop_items)
                print("   ✅ Таблица 'shop_items' создана")
            else:
                print("   ⚠️ Таблица 'shop_items' уже существует")
            
            # Проверяем, существует ли таблица student_purchases
            check_purchases = text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'student_purchases'
            """)
            result = await session.execute(check_purchases)
            purchases_exists = result.fetchone()
            
            if not purchases_exists:
                # Создаем таблицу student_purchases
                create_purchases = text("""
                    CREATE TABLE student_purchases (
                        id SERIAL PRIMARY KEY,
                        student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
                        item_id INTEGER NOT NULL REFERENCES shop_items(id) ON DELETE CASCADE,
                        price_paid INTEGER NOT NULL,
                        is_used BOOLEAN DEFAULT FALSE,
                        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                await session.execute(create_purchases)
                print("   ✅ Таблица 'student_purchases' создана")
            else:
                print("   ⚠️ Таблица 'student_purchases' уже существует")
            
            await session.commit()
            return True
            
        except Exception as e:
            print(f"   ❌ Ошибка при создании таблиц магазина: {e}")
            await session.rollback()
            return False


async def main():
    """Основная функция миграции"""
    print("🔄 Запуск миграции для магазина...")
    
    # Добавляем колонку coins
    coins_success = await add_coins_column()
    
    # Создаем таблицы магазина
    tables_success = await create_shop_tables()
    
    if coins_success and tables_success:
        print("✅ Миграция завершена успешно!")
    else:
        print("❌ Миграция завершена с ошибками!")


if __name__ == "__main__":
    asyncio.run(main())
