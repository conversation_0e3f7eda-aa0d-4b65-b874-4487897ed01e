"""
Репозиторий для работы с результатами входных тестов курса
"""
from typing import List, Optional
from sqlalchemy import select, delete, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from ..models import CourseEntryTestResult, CourseEntryQuestionResult, Student, Course, Subject, User
from ..database import get_db_session


class CourseEntryTestResultRepository:
    """Репозиторий для работы с результатами входных тестов курса"""
    
    @staticmethod
    async def get_all() -> List[CourseEntryTestResult]:
        """Получить все результаты входных тестов курса"""
        async with get_db_session() as session:
            result = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user),
                    selectinload(CourseEntryTestResult.course),
                    selectinload(CourseEntryTestResult.subject),
                    selectinload(CourseEntryTestResult.question_results)
                )
                .order_by(CourseEntryTestResult.completed_at.desc())
            )
            return list(result.scalars().all())

    @staticmethod
    async def get_by_id(test_result_id: int) -> Optional[CourseEntryTestResult]:
        """Получить результат теста по ID"""
        async with get_db_session() as session:
            result = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user),
                    selectinload(CourseEntryTestResult.course),
                    selectinload(CourseEntryTestResult.subject),
                    selectinload(CourseEntryTestResult.question_results)
                )
                .where(CourseEntryTestResult.id == test_result_id)
            )
            return result.scalar_one_or_none()

    @staticmethod
    async def get_by_student_course_subject(student_id: int, course_id: int, subject_id: int) -> Optional[CourseEntryTestResult]:
        """Получить результат теста студента по курсу и предмету"""
        async with get_db_session() as session:
            result = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user),
                    selectinload(CourseEntryTestResult.course),
                    selectinload(CourseEntryTestResult.subject),
                    selectinload(CourseEntryTestResult.question_results)
                )
                .where(
                    and_(
                        CourseEntryTestResult.student_id == student_id,
                        CourseEntryTestResult.course_id == course_id,
                        CourseEntryTestResult.subject_id == subject_id
                    )
                )
            )
            return result.scalar_one_or_none()

    @staticmethod
    async def get_by_course_and_subject(course_id: int, subject_id: int) -> List[CourseEntryTestResult]:
        """Получить все результаты тестов по курсу и предмету"""
        async with get_db_session() as session:
            result = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user),
                    selectinload(CourseEntryTestResult.course),
                    selectinload(CourseEntryTestResult.subject),
                    selectinload(CourseEntryTestResult.question_results)
                )
                .where(
                    and_(
                        CourseEntryTestResult.course_id == course_id,
                        CourseEntryTestResult.subject_id == subject_id
                    )
                )
                .order_by(CourseEntryTestResult.completed_at.desc())
            )
            return list(result.scalars().all())

    @staticmethod
    async def get_by_student_group(group_id: int) -> List[CourseEntryTestResult]:
        """Получить результаты тестов студентов группы"""
        async with get_db_session() as session:
            from ..models import student_groups
            result = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user),
                    selectinload(CourseEntryTestResult.course),
                    selectinload(CourseEntryTestResult.subject),
                    selectinload(CourseEntryTestResult.question_results)
                )
                .join(Student, CourseEntryTestResult.student_id == Student.id)
                .join(student_groups, Student.id == student_groups.c.student_id)
                .where(student_groups.c.group_id == group_id)
                .order_by(CourseEntryTestResult.completed_at.desc())
            )
            return list(result.scalars().all())

    @staticmethod
    async def create(student_id: int, course_id: int, subject_id: int, 
                    total_questions: int, correct_answers: int, 
                    question_results: List[dict]) -> CourseEntryTestResult:
        """Создать результат входного теста курса"""
        async with get_db_session() as session:
            # Вычисляем процент и статус прохождения
            score_percentage = int((correct_answers / total_questions) * 100) if total_questions > 0 else 0
            passed = score_percentage >= 70  # Порог прохождения 70%
            
            test_result = CourseEntryTestResult(
                student_id=student_id,
                course_id=course_id,
                subject_id=subject_id,
                total_questions=total_questions,
                correct_answers=correct_answers,
                score_percentage=score_percentage,
                passed=passed
            )
            session.add(test_result)
            await session.flush()  # Получаем ID
            
            # Создаем результаты ответов на вопросы
            for qr_data in question_results:
                question_result = CourseEntryQuestionResult(
                    test_result_id=test_result.id,
                    question_text=qr_data['question_text'],
                    selected_answer_text=qr_data.get('selected_answer_text'),
                    correct_answer_text=qr_data['correct_answer_text'],
                    is_correct=qr_data['is_correct'],
                    time_spent=qr_data.get('time_spent'),
                    microtopic_number=qr_data.get('microtopic_number')
                )
                session.add(question_result)
            
            await session.commit()
            await session.refresh(test_result)
            return test_result

    @staticmethod
    async def delete(test_result_id: int) -> bool:
        """Удалить результат теста"""
        async with get_db_session() as session:
            result = await session.execute(
                delete(CourseEntryTestResult).where(CourseEntryTestResult.id == test_result_id)
            )
            await session.commit()
            return result.rowcount > 0

    @staticmethod
    async def get_statistics_by_group(group_id: int) -> dict:
        """Получить статистику по группе"""
        async with get_db_session() as session:
            from ..models import student_groups, Group
            
            # Получаем всех студентов группы
            students_result = await session.execute(
                select(Student)
                .options(selectinload(Student.user))
                .join(student_groups, Student.id == student_groups.c.student_id)
                .where(student_groups.c.group_id == group_id)
            )
            all_students = list(students_result.scalars().all())
            
            # Получаем группу для определения предмета
            group_result = await session.execute(
                select(Group)
                .options(selectinload(Group.subject))
                .where(Group.id == group_id)
            )
            group = group_result.scalar_one_or_none()
            
            if not group:
                return {"completed": [], "not_completed": [], "group_name": "Неизвестная группа"}
            
            # Получаем результаты тестов для студентов группы по предмету группы
            test_results = await session.execute(
                select(CourseEntryTestResult)
                .options(
                    selectinload(CourseEntryTestResult.student).selectinload(Student.user)
                )
                .join(Student, CourseEntryTestResult.student_id == Student.id)
                .join(student_groups, Student.id == student_groups.c.student_id)
                .where(
                    and_(
                        student_groups.c.group_id == group_id,
                        CourseEntryTestResult.subject_id == group.subject_id
                    )
                )
            )
            completed_results = list(test_results.scalars().all())
            
            # Разделяем на прошедших и не прошедших тест
            completed_students = [result.student for result in completed_results]
            completed_student_ids = {student.id for student in completed_students}
            not_completed_students = [student for student in all_students if student.id not in completed_student_ids]
            
            return {
                "completed": completed_students,
                "not_completed": not_completed_students,
                "group_name": group.name,
                "subject_name": group.subject.name if group.subject else "Неизвестный предмет",
                "test_results": completed_results
            }
