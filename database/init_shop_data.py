"""
Инициализация данных магазина
"""
import asyncio
from database import init_database, ShopItemRepository


async def init_shop_items():
    """Инициализация товаров магазина"""
    print("Инициализация товаров магазина...")
    
    # Список товаров для магазина
    shop_items = [
        {
            "name": "Бонусный тест",
            "description": "Дополнительный тест для проверки знаний",
            "price": 100,
            "item_type": "bonus_test"
        },
        {
            "name": "PDF по ошибкам",
            "description": "Подробный разбор часто встречающихся ошибок",
            "price": 80,
            "item_type": "pdf"
        },
        {
            "name": "5000 тенге",
            "description": "Денежный приз",
            "price": 150,
            "item_type": "money"
        },
        {
            "name": "Консультация с преподавателем",
            "description": "Индивидуальная консультация на 30 минут",
            "price": 120,
            "item_type": "other"
        },
        {
            "name": "Дополнительные материалы",
            "description": "Эксклюзивные материалы по предмету",
            "price": 90,
            "item_type": "pdf"
        }
    ]
    
    # Проверяем, есть ли уже товары в базе
    existing_items = await ShopItemRepository.get_all_active()
    if existing_items:
        print(f"Товары уже существуют в базе данных ({len(existing_items)} шт.). Пропускаем инициализацию.")
        return
    
    # Создаем товары
    created_count = 0
    for item_data in shop_items:
        try:
            await ShopItemRepository.create(
                name=item_data["name"],
                description=item_data["description"],
                price=item_data["price"],
                item_type=item_data["item_type"]
            )
            created_count += 1
            print(f"✅ Создан товар: {item_data['name']}")
        except Exception as e:
            print(f"❌ Ошибка при создании товара {item_data['name']}: {e}")
    
    print(f"Инициализация товаров завершена. Создано: {created_count} товаров.")


async def main():
    """Основная функция инициализации"""
    await init_database()
    await init_shop_items()


if __name__ == "__main__":
    asyncio.run(main())
