import asyncio
from database import get_db_session
from sqlalchemy import text

async def check_tables():
    async with get_db_session() as session:
        # Проверяем существующие таблицы
        result = await session.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%course_entry%'"))
        tables = result.fetchall()
        print('Существующие таблицы входного теста курса:')
        for table in tables:
            print(f'  - {table[0]}')
        
        if tables:
            # Проверим структуру таблицы
            result = await session.execute(text("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'course_entry_question_results'"))
            columns = result.fetchall()
            print('\nСтруктура таблицы course_entry_question_results:')
            for col in columns:
                print(f'  - {col[0]}: {col[1]}')

if __name__ == "__main__":
    asyncio.run(check_tables())
