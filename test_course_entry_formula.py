"""
Тестовый скрипт для проверки правильности формулы входного теста курса
"""
import asyncio
from database import (
    CourseEntryTestResultRepository, StudentRepository, SubjectRepository, 
    MicrotopicRepository, get_db_session
)
from sqlalchemy import select


async def test_course_entry_formula():
    """Проверить правильность формулы входного теста курса"""
    try:
        print("🧪 ТЕСТИРОВАНИЕ ФОРМУЛЫ ВХОДНОГО ТЕСТА КУРСА")
        print("=" * 60)
        
        # Находим Олжаса
        students = await StudentRepository.get_all()
        olzhas = None
        for student in students:
            if "Олжас" in student.user.name:
                olzhas = student
                break
        
        if not olzhas:
            print("❌ Олжас не найден")
            return
        
        # Находим предмет Математика
        subjects = await SubjectRepository.get_all()
        math_subject = None
        for subject in subjects:
            if subject.name == "Математика":
                math_subject = subject
                break
        
        if not math_subject:
            print("❌ Предмет Математика не найден")
            return
        
        print(f"👤 Студент: {olzhas.user.name}")
        print(f"📚 Предмет: {math_subject.name}")
        print()
        
        # Получаем результат входного теста
        test_result = await CourseEntryTestResultRepository.get_by_student_and_subject(
            olzhas.id, math_subject.id
        )
        
        if not test_result:
            print("❌ Результат входного теста не найден")
            return
        
        print(f"📊 ОБЩИЙ РЕЗУЛЬТАТ ТЕСТА:")
        print(f"   Правильных ответов: {test_result.correct_answers}")
        print(f"   Всего вопросов: {test_result.total_questions}")
        print(f"   Процент: {test_result.score_percentage}%")
        print()
        
        # Получаем все ответы на вопросы теста
        async with get_db_session() as session:
            from database.models import CourseEntryQuestionResult
            result = await session.execute(
                select(CourseEntryQuestionResult)
                .where(CourseEntryQuestionResult.test_result_id == test_result.id)
                .order_by(CourseEntryQuestionResult.id)
            )
            question_results = list(result.scalars().all())
        
        print(f"📝 ДЕТАЛИ ПО ВОПРОСАМ ({len(question_results)} шт.):")
        
        # Группируем по микротемам вручную
        manual_microtopic_stats = {}
        
        for i, qr in enumerate(question_results, 1):
            status = "✅" if qr.is_correct else "❌"
            microtopic = qr.microtopic_number or "Без микротемы"
            
            print(f"   {i:2d}. {status} Микротема: {microtopic}, Время: {qr.time_spent}с")
            
            # Считаем вручную
            if qr.microtopic_number:
                if qr.microtopic_number not in manual_microtopic_stats:
                    manual_microtopic_stats[qr.microtopic_number] = {
                        'total': 0,
                        'correct': 0
                    }
                
                manual_microtopic_stats[qr.microtopic_number]['total'] += 1
                if qr.is_correct:
                    manual_microtopic_stats[qr.microtopic_number]['correct'] += 1
        
        print()
        print("🧮 РУЧНОЙ ПОДСЧЕТ ПО МИКРОТЕМАМ:")
        
        # Получаем названия микротем
        microtopics = await MicrotopicRepository.get_by_subject(math_subject.id)
        microtopic_names = {mt.number: mt.name for mt in microtopics}
        
        manual_percentages = {}
        for microtopic_num in sorted(manual_microtopic_stats.keys()):
            stats = manual_microtopic_stats[microtopic_num]
            percentage = int((stats['correct'] / stats['total']) * 100) if stats['total'] > 0 else 0
            manual_percentages[microtopic_num] = percentage
            
            microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
            print(f"   • {microtopic_name}: {stats['correct']}/{stats['total']} = {percentage}%")
        
        print()
        print("🔧 РЕЗУЛЬТАТ ИЗ РЕПОЗИТОРИЯ:")
        
        # Получаем статистику из репозитория
        repo_stats = await CourseEntryTestResultRepository.get_microtopic_statistics(test_result.id)
        
        repo_percentages = {}
        for microtopic_num in sorted(repo_stats.keys()):
            stats = repo_stats[microtopic_num]
            repo_percentages[microtopic_num] = stats['percentage']
            
            microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
            print(f"   • {microtopic_name}: {stats['correct']}/{stats['total']} = {stats['percentage']}%")
        
        print()
        print("✅ СРАВНЕНИЕ РЕЗУЛЬТАТОВ:")
        
        all_match = True
        for microtopic_num in sorted(set(manual_percentages.keys()) | set(repo_percentages.keys())):
            manual_pct = manual_percentages.get(microtopic_num, 0)
            repo_pct = repo_percentages.get(microtopic_num, 0)
            
            microtopic_name = microtopic_names.get(microtopic_num, f"Микротема {microtopic_num}")
            
            if manual_pct == repo_pct:
                print(f"   ✅ {microtopic_name}: {manual_pct}% = {repo_pct}% ✓")
            else:
                print(f"   ❌ {microtopic_name}: {manual_pct}% ≠ {repo_pct}% ✗")
                all_match = False
        
        print()
        if all_match:
            print("🎉 ВСЕ ПРОЦЕНТЫ СОВПАДАЮТ! Формула работает правильно!")
        else:
            print("💥 ЕСТЬ РАСХОЖДЕНИЯ! Нужно проверить формулу!")
        
        print()
        print("📋 ФОРМУЛА:")
        print("   % по микротеме = (правильные ответы по микротеме в тесте) / (всего вопросов по микротеме в тесте) × 100%")
        print()
        print("🔍 ИСТОЧНИК ДАННЫХ:")
        print("   - Данные берутся ТОЛЬКО из входного теста курса")
        print("   - НЕ из общей статистики по всем ДЗ")
        print("   - Только те вопросы, которые попали в конкретный тест из 30 случайных")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_course_entry_formula())
