import asyncio
from database import (
    CourseEntryTestResultRepository, StudentRepository, SubjectRepository
)

async def create_math_test_for_olzhas():
    try:
        # Находим Олжаса
        students = await StudentRepository.get_all()
        olzhas = None
        for student in students:
            if "Олжас" in student.user.name:
                olzhas = student
                break
        
        if not olzhas:
            print("❌ Олжас не найден")
            return
        
        # Находим предмет Математика
        subjects = await SubjectRepository.get_all()
        math_subject = None
        for subject in subjects:
            if subject.name == "Математика":
                math_subject = subject
                break
        
        if not math_subject:
            print("❌ Предмет Математика не найден")
            return
        
        # Проверяем, есть ли уже результат
        existing_result = await CourseEntryTestResultRepository.get_by_student_and_subject(
            olzhas.id, math_subject.id
        )
        
        if existing_result:
            print(f"✅ У Олжаса уже есть результат по математике: {existing_result.score_percentage}%")
            return
        
        # Получаем вопросы по математике
        questions = await CourseEntryTestResultRepository.get_random_questions_for_subject(
            math_subject.id, 30
        )
        
        if len(questions) < 30:
            print(f"❌ Недостаточно вопросов по математике (найдено: {len(questions)})")
            # Возьмем сколько есть
            if len(questions) == 0:
                return
        
        # Создаем результаты ответов (пусть Олжас ответит правильно на 22 из 30)
        question_results = []
        correct_count = min(22, len(questions))
        
        for i, question in enumerate(questions):
            is_correct = i < correct_count
            
            # Выбираем ответ
            correct_answer = None
            wrong_answer = None
            
            for answer in question.answer_options:
                if answer.is_correct:
                    correct_answer = answer
                else:
                    wrong_answer = answer
            
            selected_answer = correct_answer if is_correct else wrong_answer
            
            question_results.append({
                'question_id': question.id,
                'selected_answer_id': selected_answer.id if selected_answer else None,
                'is_correct': is_correct,
                'time_spent': 25 + i,  # Разное время ответа
                'microtopic_number': question.microtopic_number
            })
        
        # Создаем результат теста
        test_result = await CourseEntryTestResultRepository.create_test_result(
            student_id=olzhas.id,
            subject_id=math_subject.id,
            question_results=question_results
        )
        
        print(f"✅ Результат входного теста по математике создан для Олжаса: {test_result.score_percentage}%")
        print(f"   Правильных ответов: {test_result.correct_answers}/{test_result.total_questions}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(create_math_test_for_olzhas())
